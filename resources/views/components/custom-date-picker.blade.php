{{-- Custom Date Picker Component with European Format --}}
<div class="custom-date-picker-wrapper" id="{{ $getComponentId() }}">
    @if($showLabel && $label)
        <label for="{{ $id }}" class="form-label">
            {{ $label }}
            @if($required)
                <span class="text-danger">*</span>
            @endif
        </label>
    @endif
    
    <input
        type="text"
        name="{{ $name }}"
        id="{{ $id }}"
        value="{{ $value }}"
        placeholder="{{ $placeholder }}"
        {{ $required ? 'required' : '' }}
        {{ $disabled ? 'disabled' : '' }}
        class="form-control {{ $class }}{{ $hasError() ? ' is-invalid' : '' }}"
        data-flatpickr-config='{
            "dateFormat": "Y-m-d",
            "altInput": true,
            "altFormat": "d/m/Y",
            @if($minDate)
            "minDate": "{{ $minDate }}",
            @endif
            "allowInput": true,
            "disabled": {{ $disabled ? 'true' : 'false' }}
        }'
        @if($onChange)
            data-on-change="{{ $onChange }}"
        @endif
    >
    
    @if($hasError())
        <div class="invalid-feedback">{{ $getErrorMessage() }}</div>
    @endif
</div>

{{-- Component Styles --}}
@once
<style>
    /* Container styling with subtle drop shadow */
    .custom-date-picker-wrapper {
        position: relative;
        border-radius: 0.375rem;
    }

    /* Ensure Flatpickr input styling matches Bootstrap form controls */
    .flatpickr-input {
        background-color: #fff !important;
        cursor: pointer !important;
    }

    .flatpickr-input:disabled {
        background-color: #e9ecef !important;
        cursor: not-allowed !important;
        opacity: 1;
    }

    /* Style the Flatpickr wrapper */
    .custom-date-picker-wrapper .flatpickr-wrapper {
        position: relative;
        width: 100%;
    }

    /* Calendar icon styling - Multiple selectors for different Flatpickr structures */
    .custom-date-picker-wrapper .flatpickr-input::after,
    .custom-date-picker-wrapper::after {
        content: '\f237'; /* Boxicons calendar */
        font-family: 'boxicons';
        font-weight: normal;
        position: absolute;
        right: 12px;
        top: 70%;
        transform: translateY(-50%);
        color: #6c757d;
        pointer-events: none;
        z-index: 1;
        font-size: 0.875rem;
        line-height: 1;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }



    /* Fallback for Tabler Icons if Boxicons fails */
    .custom-date-picker-wrapper .flatpickr-input.tabler-fallback::after,
    .custom-date-picker-wrapper.tabler-fallback::after {
        content: '\ea5f'; /* Tabler Icons chevron-down */
        font-family: 'tabler-icons';
    }

    /* Fallback for Bootstrap Icons if both Boxicons and Tabler fail */
    .custom-date-picker-wrapper .flatpickr-input.bootstrap-icons-fallback::after,
    .custom-date-picker-wrapper.bootstrap-icons-fallback::after {
        content: '\f282'; /* Bootstrap Icons chevron-down */
        font-family: 'bootstrap-icons';
    }

    /* Unicode fallback if all icon fonts fail to load */
    .custom-date-picker-wrapper .flatpickr-input.unicode-fallback::after,
    .custom-date-picker-wrapper.unicode-fallback::after {
        content: '📅'; /* Calendar emoji as fallback */
        font-family: sans-serif;
        font-size: 0.875rem;
    }

    /* Disabled state styling */
    .custom-date-picker-wrapper .flatpickr-input.disabled::after,
    .custom-date-picker-wrapper.disabled::after,
    .custom-date-picker-wrapper .flatpickr-input.tabler-fallback.disabled::after,
    .custom-date-picker-wrapper.tabler-fallback.disabled::after,
    .custom-date-picker-wrapper .flatpickr-input.bootstrap-icons-fallback.disabled::after,
    .custom-date-picker-wrapper.bootstrap-icons-fallback.disabled::after,
    .custom-date-picker-wrapper .flatpickr-input.unicode-fallback.disabled::after,
    .custom-date-picker-wrapper.unicode-fallback.disabled::after {
        color: #adb5bd;
    }

    /* Calendar styling enhancements */
    .flatpickr-calendar {
        z-index: 9999 !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    }

    /* Today date indicator - more subtle with success green */
    .flatpickr-calendar .flatpickr-day.today {
        background-color: rgba(25, 135, 84, 0.3) !important;
        color: #495057 !important;
        border-color: rgba(25, 135, 84, 0.5) !important;
    }

    /* Selected date - full prominence with success green */
    .flatpickr-calendar .flatpickr-day.selected,
    .flatpickr-calendar .flatpickr-day.selected:hover {
        background-color: rgb(var(--primary-rgb)) !important;
        color: #fff !important;
        border-color: rgb(var(--primary-rgb)) !important;
        opacity: 1 !important;
    }

    /* Today + selected date */
    .flatpickr-calendar .flatpickr-day.today.selected {
        background-color: rgb(var(--primary-rgb)) !important;
        color: #fff !important;
        opacity: 1 !important;
    }

    /* Hover effects with success green */
    .flatpickr-calendar .flatpickr-day:hover {
        background-color: rgba(25, 135, 84, 0.1) !important;
        border-color: rgba(25, 135, 84, 0.3) !important;
    }

    /* Month navigation styling */
    .flatpickr-calendar .flatpickr-months {
        color: rgb(var(--primary-rgb)) !important;
        background-color: #f8f9fa;
    }

    .flatpickr-current-month .flatpickr-monthDropdown-months {
        background-color: transparent !important;
    }

    /* Weekday headers */
    .flatpickr-calendar .flatpickr-weekdays {
        color: rgb(var(--primary-rgb)) !important;
        background-color: #f8f9fa;
    }

    .flatpickr-calendar .flatpickr-weekday {
        color: #6c757d;
        font-weight: 600;
    }

    /* Dark Mode Compatibility */
    [data-theme-mode="dark"] .flatpickr-calendar .flatpickr-day.today {
        color: #fff !important;
    }

    /* Dark mode calendar icon styling */
    [data-theme-mode="dark"] .custom-date-picker-wrapper .flatpickr-input::after,
    [data-theme-mode="dark"] .custom-date-picker-wrapper::after,
    [data-theme-mode="dark"] .custom-date-picker-wrapper .flatpickr-input.tabler-fallback::after,
    [data-theme-mode="dark"] .custom-date-picker-wrapper.tabler-fallback::after,
    [data-theme-mode="dark"] .custom-date-picker-wrapper .flatpickr-input.bootstrap-icons-fallback::after,
    [data-theme-mode="dark"] .custom-date-picker-wrapper.bootstrap-icons-fallback::after,
    [data-theme-mode="dark"] .custom-date-picker-wrapper .flatpickr-input.unicode-fallback::after,
    [data-theme-mode="dark"] .custom-date-picker-wrapper.unicode-fallback::after {
        color: #adb5bd !important;
    }
</style>
@endonce

{{-- Component JavaScript --}}
@once
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all custom date pickers on the page
    initializeCustomDatePickers();
});

function initializeCustomDatePickers() {
    const datePickerInputs = document.querySelectorAll('[data-flatpickr-config]');
    
    datePickerInputs.forEach(function(input) {
        // Skip if already initialized
        if (input._flatpickr) {
            return;
        }
        
        try {
            // Parse configuration from data attribute
            const config = JSON.parse(input.getAttribute('data-flatpickr-config'));

            // Get initial value from the input
            const initialValue = input.value;

            // Set default date if initial value exists
            if (initialValue && initialValue.trim() !== '') {
                // Validate the date before setting it as defaultDate
                const testDate = new Date(initialValue);
                if (!isNaN(testDate.getTime())) {
                    config.defaultDate = initialValue;
                    console.log('Setting defaultDate for', input.id, ':', initialValue);
                } else {
                    console.warn('Invalid initial date value for', input.id, ':', initialValue);
                }
            }

            // Remove minDate restriction if it's set to "today" to allow past dates
            if (config.minDate === 'today' || config.minDate === '') {
                delete config.minDate;
            }

            // Add European date format validation for manual input
            config.parseDate = function(datestr, format) {
                console.log('Parsing date:', datestr, 'with format:', format);

                // Handle ISO format YYYY-MM-DD (for defaultDate and server values)
                const isoRegex = /^(\d{4})-(\d{1,2})-(\d{1,2})$/;
                const isoMatch = datestr.match(isoRegex);

                if (isoMatch) {
                    const year = parseInt(isoMatch[1], 10);
                    const month = parseInt(isoMatch[2], 10) - 1; // Month is 0-indexed
                    const day = parseInt(isoMatch[3], 10);

                    // Validate ISO date components
                    if (year >= 1900 && month >= 0 && month <= 11 && day >= 1 && day <= 31) {
                        const date = new Date(year, month, day);
                        console.log('Successfully parsed ISO date:', date);
                        return date;
                    }
                }

                // Handle European format DD/MM/YYYY or DD.MM.YYYY or DD-MM-YYYY
                const europeanRegex = /^(\d{1,2})[\/\.\-](\d{1,2})[\/\.\-](\d{4})$/;
                const europeanMatch = datestr.match(europeanRegex);

                if (europeanMatch) {
                    const day = parseInt(europeanMatch[1], 10);
                    const month = parseInt(europeanMatch[2], 10) - 1; // Month is 0-indexed
                    const year = parseInt(europeanMatch[3], 10);

                    // Validate European date components
                    if (day >= 1 && day <= 31 && month >= 0 && month <= 11 && year >= 1900) {
                        const date = new Date(year, month, day);
                        console.log('Successfully parsed European date:', date);
                        return date;
                    }
                }

                console.log('Failed to parse date:', datestr);
                // Return undefined to let Flatpickr use its default parsing
                return undefined;
            };

            // Get onChange callback if specified
            const onChangeCallback = input.getAttribute('data-on-change');

            // Add onChange handler if specified
            if (onChangeCallback) {
                config.onChange = function(selectedDates, dateStr, instance) {
                    try {
                        // Execute the callback function
                        eval(onChangeCallback);
                    } catch (error) {
                        console.error('Error executing date picker onChange callback:', error);
                    }
                };
            }
            
            // Initialize Flatpickr with error handling
            let picker;
            try {
                picker = flatpickr(input, config);
                console.log('Successfully initialized Flatpickr for:', input.id, 'with initial value:', initialValue);
            } catch (flatpickrError) {
                console.error('Flatpickr initialization failed for', input.id, ':', flatpickrError);

                // Try initializing without defaultDate if it caused the error
                if (config.defaultDate) {
                    console.log('Retrying initialization without defaultDate for:', input.id);
                    const fallbackConfig = { ...config };
                    delete fallbackConfig.defaultDate;

                    try {
                        picker = flatpickr(input, fallbackConfig);
                        console.log('Fallback initialization successful for:', input.id);

                        // Try to set the date after initialization
                        if (initialValue && initialValue.trim() !== '') {
                            setTimeout(() => {
                                try {
                                    picker.setDate(initialValue, false);
                                    console.log('Successfully set date after fallback initialization:', initialValue);
                                } catch (setDateError) {
                                    console.warn('Failed to set date after fallback initialization:', setDateError);
                                }
                            }, 0);
                        }
                    } catch (fallbackError) {
                        console.error('Fallback initialization also failed for', input.id, ':', fallbackError);
                        return; // Skip this input if both attempts fail
                    }
                } else {
                    return; // Skip this input if initialization fails without defaultDate
                }
            }

            // Store reference for potential cleanup
            if (picker) {
                input._flatpickr = picker;
                console.log('Stored Flatpickr reference for:', input.id);

                // Check and apply icon font fallbacks
                setTimeout(() => {
                    checkAndApplyIconFallback(input);
                }, 100);
            }

        } catch (error) {
            console.error('Error initializing custom date picker for', input.id, ':', error);
        }
    });
}

// Function to reinitialize date pickers (useful for dynamic content)
window.reinitializeCustomDatePickers = function() {
    initializeCustomDatePickers();
};

// Function to get date picker instance
window.getCustomDatePicker = function(elementId) {
    const element = document.getElementById(elementId);
    return element ? element._flatpickr : null;
};

// Function to set date programmatically
window.setCustomDatePickerValue = function(elementId, date) {
    const picker = getCustomDatePicker(elementId);
    if (picker) {
        picker.setDate(date);
    }
};

// Function to clear date picker
window.clearCustomDatePicker = function(elementId) {
    const picker = getCustomDatePicker(elementId);
    if (picker) {
        picker.clear();
    }
};

// Function to check icon font availability and apply fallbacks
function checkAndApplyIconFallback(input) {
    const wrapper = input.closest('.custom-date-picker-wrapper');
    if (!wrapper) return;

    // Try to find the target element for applying fallback classes
    let targetElement = wrapper.querySelector('.flatpickr-wrapper');
    if (!targetElement) {
        targetElement = wrapper.querySelector('.flatpickr-input');
    }
    if (!targetElement) {
        targetElement = wrapper; // Fallback to wrapper itself
    }

    // Test if Boxicons is available (primary icon font)
    const testElement = document.createElement('span');
    testElement.style.fontFamily = 'boxicons';
    testElement.style.position = 'absolute';
    testElement.style.left = '-9999px';
    testElement.style.fontSize = '16px';
    testElement.innerHTML = '&#xf237;'; // Boxicons calendar
    document.body.appendChild(testElement);

    const boxiconsWidth = testElement.offsetWidth;
    document.body.removeChild(testElement);

    // If Boxicons didn't load, try Tabler Icons fallback
    if (boxiconsWidth < 10) {
        console.log('Boxicons not available, trying Tabler Icons fallback for:', input.id);

        // Test Tabler Icons
        const testElement2 = document.createElement('span');
        testElement2.style.fontFamily = 'tabler-icons';
        testElement2.style.position = 'absolute';
        testElement2.style.left = '-9999px';
        testElement2.style.fontSize = '16px';
        testElement2.innerHTML = '&#xea5f;'; // Tabler chevron-down
        document.body.appendChild(testElement2);

        const tablerWidth = testElement2.offsetWidth;
        document.body.removeChild(testElement2);

        if (tablerWidth >= 10) {
            targetElement.classList.add('tabler-fallback');
            console.log('Applied Tabler Icons fallback for:', input.id);
        } else {
            // Test Bootstrap Icons as final icon font fallback
            console.log('Tabler Icons not available, trying Bootstrap Icons fallback for:', input.id);

            const testElement3 = document.createElement('span');
            testElement3.style.fontFamily = 'bootstrap-icons';
            testElement3.style.position = 'absolute';
            testElement3.style.left = '-9999px';
            testElement3.style.fontSize = '16px';
            testElement3.innerHTML = '&#xf282;'; // Bootstrap chevron-down
            document.body.appendChild(testElement3);

            const bootstrapWidth = testElement3.offsetWidth;
            document.body.removeChild(testElement3);

            if (bootstrapWidth >= 10) {
                targetElement.classList.add('bootstrap-icons-fallback');
                console.log('Applied Bootstrap Icons fallback for:', input.id);
            } else {
                // Use Unicode fallback
                targetElement.classList.add('unicode-fallback');
                console.log('Applied Unicode fallback for:', input.id);
            }
        }
    } else {
        console.log('Boxicons loaded successfully for:', input.id);
    }
}
</script>
@endonce
